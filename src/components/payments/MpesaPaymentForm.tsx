import { useState, useEffect } from 'react';
import { Smartphone, AlertCircle, CheckCircle, Clock, X } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import mpesaApi, { MpesaPaymentRequest, MpesaTransactionStatus } from '../../services/mpesaApi';
import toast from 'react-hot-toast';

interface MpesaPaymentFormProps {
  walletAddress: string;
  packageId: number;
  amount: number; // USD amount
  referrerAddress?: string;
  onSuccess: (transactionId: string) => void;
  onCancel: () => void;
}

type PaymentStep = 'input' | 'processing' | 'waiting' | 'completed' | 'failed';

export function MpesaPaymentForm({
  walletAddress,
  packageId,
  amount,
  referrerAddress,
  onSuccess,
  onCancel
}: MpesaPaymentFormProps) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [step, setStep] = useState<PaymentStep>('input');
  const [loading, setLoading] = useState(false);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const [checkoutRequestId, setCheckoutRequestId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(120); // 2 minutes timeout

  const kesAmount = Math.round(amount * 149.25);

  // Format phone number as user types
  const handlePhoneNumberChange = (value: string) => {
    // Remove any non-digit characters
    const cleaned = value.replace(/\D/g, '');
    
    // Format for display
    let formatted = cleaned;
    if (cleaned.startsWith('254')) {
      formatted = cleaned;
    } else if (cleaned.startsWith('0')) {
      formatted = '254' + cleaned.substring(1);
    } else if (cleaned.length <= 9) {
      formatted = '254' + cleaned;
    }
    
    setPhoneNumber(formatted);
  };

  // Validate phone number
  const isValidPhoneNumber = mpesaApi.validatePhoneNumber(phoneNumber);

  // Handle payment initiation
  const handleInitiatePayment = async () => {
    if (!isValidPhoneNumber) {
      toast.error('Please enter a valid Kenyan phone number');
      return;
    }

    setLoading(true);
    setError(null);
    setStep('processing');

    try {
      const paymentData: MpesaPaymentRequest = {
        walletAddress,
        packageId,
        phoneNumber,
        amount,
        referrerAddress
      };

      const response = await mpesaApi.initiatePayment(paymentData);

      if (response.success && response.transactionId && response.checkoutRequestId) {
        setTransactionId(response.transactionId);
        setCheckoutRequestId(response.checkoutRequestId);
        setStep('waiting');
        setCountdown(120);
        
        toast.success('Payment request sent to your phone. Please check your M-Pesa notifications.');
        
        // Start polling for payment status
        startStatusPolling(response.checkoutRequestId);
      } else {
        setError(response.error || 'Failed to initiate payment');
        setStep('failed');
        toast.error(response.error || 'Failed to initiate payment');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to initiate payment');
      setStep('failed');
      toast.error('Failed to initiate payment');
    } finally {
      setLoading(false);
    }
  };

  // Poll payment status
  const startStatusPolling = (checkoutId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await mpesaApi.queryPaymentStatus(checkoutId);
        
        if (status.success && status.transaction) {
          const txStatus = status.transaction.status;
          
          if (txStatus === 'completed') {
            setStep('completed');
            clearInterval(pollInterval);
            toast.success('Payment completed successfully!');
            onSuccess(status.transaction.id);
          } else if (txStatus === 'failed' || txStatus === 'cancelled') {
            setStep('failed');
            setError(status.mpesaStatus?.resultDesc || 'Payment failed');
            clearInterval(pollInterval);
            toast.error('Payment failed or was cancelled');
          }
        }
      } catch (error) {
        console.error('Error polling payment status:', error);
      }
    }, 3000); // Poll every 3 seconds

    // Stop polling after 2 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      if (step === 'waiting') {
        setStep('failed');
        setError('Payment timeout. Please try again.');
        toast.error('Payment timeout. Please try again.');
      }
    }, 120000);
  };

  // Countdown timer
  useEffect(() => {
    if (step === 'waiting' && countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [step, countdown]);

  const renderStepContent = () => {
    switch (step) {
      case 'input':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                M-Pesa Phone Number
              </label>
              <Input
                type="tel"
                placeholder="254712345678"
                value={phoneNumber}
                onChange={(e) => handlePhoneNumberChange(e.target.value)}
                className={`${!isValidPhoneNumber && phoneNumber ? 'border-red-500' : ''}`}
                maxLength={12}
              />
              {phoneNumber && !isValidPhoneNumber && (
                <p className="text-xs text-red-600 mt-1">
                  Please enter a valid Kenyan phone number (254XXXXXXXXX)
                </p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Enter your M-Pesa registered phone number
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600 dark:text-gray-400">Package Amount:</span>
                <span className="font-medium">${amount} USD</span>
              </div>
              <div className="flex justify-between items-center text-sm mt-1">
                <span className="text-gray-600 dark:text-gray-400">M-Pesa Amount:</span>
                <span className="font-medium">KES {kesAmount.toLocaleString()}</span>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={handleInitiatePayment}
                disabled={!isValidPhoneNumber || loading}
                loading={loading}
                className="flex-1"
              >
                <Smartphone className="h-4 w-4 mr-2" />
                Send Payment Request
              </Button>
              <Button variant="outline" onClick={onCancel} className="flex-1">
                Cancel
              </Button>
            </div>
          </div>
        );

      case 'processing':
        return (
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Initiating Payment
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Please wait while we process your request...
              </p>
            </div>
          </div>
        );

      case 'waiting':
        return (
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="relative">
                <Smartphone className="h-16 w-16 text-blue-600" />
                <div className="absolute -top-1 -right-1">
                  <div className="animate-ping absolute inline-flex h-4 w-4 rounded-full bg-green-400 opacity-75"></div>
                  <div className="relative inline-flex rounded-full h-4 w-4 bg-green-500"></div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Check Your Phone
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                An M-Pesa payment request has been sent to {phoneNumber}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Enter your M-Pesa PIN to complete the payment
              </p>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div className="flex items-center justify-center space-x-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-700 dark:text-blue-300">
                  Time remaining: {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
                </span>
              </div>
            </div>

            <Button variant="outline" onClick={onCancel} className="w-full">
              Cancel Payment
            </Button>
          </div>
        );

      case 'completed':
        return (
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-green-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Payment Successful!
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Your M-Pesa payment has been completed successfully
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Your USDT package is being processed...
              </p>
            </div>

            {transactionId && (
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                <p className="text-xs text-green-700 dark:text-green-300">
                  Transaction ID: {transactionId}
                </p>
              </div>
            )}
          </div>
        );

      case 'failed':
        return (
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <X className="h-16 w-16 text-red-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Payment Failed
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {error || 'Something went wrong with your payment'}
              </p>
            </div>

            <div className="flex space-x-3">
              <Button onClick={() => setStep('input')} className="flex-1">
                Try Again
              </Button>
              <Button variant="outline" onClick={onCancel} className="flex-1">
                Cancel
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Smartphone className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            M-Pesa Payment
          </h2>
          <Badge variant="secondary">Mobile Money</Badge>
        </div>
        
        {renderStepContent()}
      </CardContent>
    </Card>
  );
}
