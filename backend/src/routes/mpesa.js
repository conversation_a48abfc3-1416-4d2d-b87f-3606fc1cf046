import express from 'express';
import mpesaController from '../controllers/mpesaController.js';
import {
  createRateLimit,
  validateWalletAddress,
  validatePhoneNumber,
  validateAmount,
  validatePackageId,
  logRequest
} from '../middleware/auth.js';

const router = express.Router();

// Apply rate limiting to all M-Pesa routes
router.use(createRateLimit(15 * 60 * 1000, 50)); // 50 requests per 15 minutes

// Apply request logging
router.use(logRequest);

// Initiate M-Pesa payment
router.post('/initiate-payment', [
  validateWalletAddress,
  validatePhoneNumber,
  validateAmount,
  validatePackageId
], mpesaController.initiatePayment);

// M-Pesa callback endpoint (no rate limiting for callbacks)
router.post('/callback/:transactionId', mpesaController.handleCallback);

// M-Pesa timeout endpoint
router.post('/timeout/:transactionId', mpesaController.handleTimeout);

// Query payment status
router.get('/status/:checkoutRequestId', mpesaController.queryPaymentStatus);

// Get transaction history for a wallet
router.get('/transactions/:walletAddress', mpesaController.getTransactionHistory);

export default router;
